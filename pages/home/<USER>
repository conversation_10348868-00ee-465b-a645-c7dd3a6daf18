<template>
  <div
    class="relative fx-cer flex-col min-h-screen px-4 pb-12 overflow-hidden"
    :class="{ 'pt-10': isMobile }"
  >
    <!-- 背景图 -->
    <img src="~/assets/image/bgTri.png" alt="" class="absolute top-0 left-0 w-100 h-100" />
    <img
      src="~/assets/image/bgCircle.png"
      alt=""
      class="absolute bottom-[-160px] right-0 w-100 h-100"
    />

    <!-- 数据卡片展示区域 -->
    <div v-if="showResults" class="w-full max-w-[760px] space-y-4 mb-6">
      <div class="talent-card-wrapper">
        <SearchCard :candidates="cards" />
      </div>
    </div>

    <!-- 原始 Logo 区域 -->
    <div v-else class="flex flex-col items-center mb-6 text-center logo-area">
      <img
        :src="isDark ? '/image/darklogo2.png' : '/image/newlogo1.png'"
        class="w-[180px] sm:w-[240px] h-auto mb-2"
        :alt="isDark ? 'DINQ dark logo' : 'DINQ logo'"
      />
      <div class="text-[#5E5E5E] text-sm sm:text-5 font-400">
        Intelligent matching of top talent
      </div>
    </div>

    <!-- 轮播区域 -->
    <div
      v-if="!showResults"
      class="h-auto mb-6 rounded-4 px-4 py-2 fx-cer gap-4 bg-gray-100"
      :class="{ 'dark:bg-[#242425]': users.length > 0 }"
    >
      <span>Poach Window</span>
      <Transition name="fade">
        <div>
          <div
            v-if="users.length > 0"
            class="flex flex-wrap items-center justify-center gap-3 text-orange-500 cursor-pointer text-sm sm:text-4 animate__animated animate__fadeInDown"
            @click="handleUserClick(currentUser)"
            :key="currentUser?.id"
          >
            <img
              :src="currentUser.avatarUrl"
              alt="Avatar"
              class="w-8 h-8 sm:w-10 sm:h-10 rounded-full"
            />
            <span>{{ currentUser.name }}</span>
            <img
              :src="currentUser.oldCompanyIcon"
              alt="Old"
              class="w-8 h-8 sm:w-10 sm:h-10 rounded-full"
            />
            <span>→</span>
            <img
              :src="currentUser.newCompanyIcon"
              alt="New"
              class="w-8 h-8 sm:w-10 sm:h-10 rounded-full"
            />
            <span>${{ currentUser.salary }}</span>
          </div>
        </div>
      </Transition>
    </div>
    <!-- 搜索框 -->
    <div
      class="w-full max-w-[760px] h-auto border border-black rounded-[10px] bg-white px-4 py-3 dark:bg-[#242425] z-10"
    >
      <input
        v-model="inputValue"
        class="w-full px-2 py-2 text-sm sm:text-base rounded focus:outline-none focus:border-none mb-2 dark:bg-[#242425]"
        placeholder="AI Agent"
      />
      <div class="flex flex-wrap items-center justify-between gap-3">
        <!-- 筛选按钮 -->
        <div class="relative inline-block text-left">
          <button
            @click="toggleDropdown"
            class="p-2 rounded hover:bg-gray-200 bg-transparent dark:hover:bg-gray-200/20"
          >
            <img :src="slider" alt="" class="w-5 h-5" />
          </button>

          <!-- 筛选下拉菜单 -->
          <div
            v-if="dropdownOpen && !isMobile"
            class="absolute z-10 mt-2 w-44 flex flex-col gap-2 bg-white p-2 border border-[#DDD] dark:border-[#252525] rounded-2 shadow-md dark:bg-[#1B1B1B]"
          >
            <div
              v-for="option in options"
              :key="option.value"
              @click="toggleSelection(option.value)"
              class="px-4 py-2 flex items-center gap-2 cursor-pointer w-full rounded-2"
              :class="{
                'bg-[#FFF5F1] font-semibold text-[#CB7C5D] border border-[#E5C7BB] dark:bg-[#353535] dark:text-white dark:border-none':
                  selected === option.value,
                'bg-white border border-[#E6E6E6] hover:bg-gray-50 text-gray-700 dark:border-none dark:bg-[#212121] dark:hover:bg-[#353535] dark:text-[#7A7A7A]':
                  selected !== option.value,
              }"
            >
              <img :src="option.icon" alt="" />
              <span>{{ option.label }}</span>
            </div>
          </div>
        </div>

        <!-- 移动端底部弹出 -->
        <Transition name="fade">
          <div
            v-if="dropdownOpen && isMobile"
            class="fixed bottom-0 left-0 right-0 bg-white dark:bg-[#1B1B1B] z-50 rounded-t-[20px] p-4 border-t border-[#DDD] dark:border-[#2B2B2B] shadow-xl"
          >
            <!-- 关闭按钮 -->
            <div class="flex justify-end mb-4">
              <button
                class="text-gray-500 dark:text-gray-300 bg-transparent"
                @click="dropdownOpen = false"
              >
                <img src="~/assets/image/Close-middle.svg" alt="" class="w-5 h-5" />
              </button>
            </div>

            <!-- 筛选项内容 -->
            <div class="flex flex-col gap-2">
              <div
                v-for="option in options"
                :key="option.value"
                @click="toggleSelection(option.value)"
                class="px-4 py-3 flex items-center gap-2 cursor-pointer rounded-2"
                :class="{
                  'bg-[#FFF5F1] font-semibold text-[#CB7C5D] border border-[#E5C7BB] dark:bg-[#353535] dark:text-white dark:border-none':
                    selected === option.value,
                  'bg-white border border-[#E6E6E6] hover:bg-gray-50 text-gray-700 dark:border-none dark:bg-[#212121] dark:hover:bg-[#353535] dark:text-[#7A7A7A]':
                    selected !== option.value,
                }"
              >
                <img :src="option.icon" alt="" />
                <span>{{ option.label }}</span>
              </div>
            </div>
          </div>
        </Transition>

        <!-- 提交按钮 -->
        <button
          class="btn btn-primary w-[70px] h-[36px] border border-black rounded-2 dark:border-[#323232]"
          :class="{
            'bg-black text-white': isInputValid,
            'bg-gray-300 cursor-not-allowed dark:bg-[#323232]': !isInputValid,
          }"
          :disabled="!isInputValid"
          @click="submitQuery"
        >
          DINQ
        </button>
      </div>
    </div>

    <!-- 加载弹窗 -->
    <div
      v-if="loading"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div
        class="bg-white dark:bg-black dark:border-[#27282D] p-6 rounded-lg shadow-xl w-[90%] max-w-[360px] h-[198px] flex flex-col items-center justify-center gap-2 text-center"
      >
        <p class="text-base font-bold sm:text-6">DINQing</p>
        <p class="text-[#585858] text-sm">Searching talent</p>
        <img
          src="~/assets/image/gif2.gif"
          alt="loading"
          class="btn-icon-dark w-16 h-16 sm:w-20 sm:h-20"
        />
        <img
          src="~/assets/image/gif.gif"
          alt="loading"
          class="btn-icon-light w-16 h-16 sm:w-20 sm:h-20"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, defineExpose } from 'vue'
  import slider from '/assets/image/slider.png'
  import SearchCard from '../../components/SearchCard/index.vue'
  import github from '/assets/image/github 1.svg'
  import scholar from '/assets/image/scholar 1.svg'
  import company from '/assets/image/office-building 1.svg'
  import 'animate.css'
  // 控制状态
  const dropdownOpen = ref(false)
  const selected = ref<string>('Scholar')
  const inputValue = ref('')
  const loading = ref(false)
  const showResults = ref(false)
  const cards = ref<Card[]>([])
  // 检测深色模式
  const isDark = ref(false)
  const isMobile = ref(false)
  const operator = ref(0)

  onMounted(() => {
    const checkMobile = () => {
      isMobile.value = window.innerWidth <= 768
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
  })

  onMounted(() => {
    isDark.value = document.documentElement.classList.contains('dark')
    const observer = new MutationObserver(() => {
      isDark.value = document.documentElement.classList.contains('dark')
    })
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })
  })

  // 模拟选项
  const options = [
    { label: 'Scholar', value: 'Scholar', icon: scholar },
    { label: 'Github', value: 'Github', icon: github },
    { label: 'Company', value: 'Company', icon: company },
  ]

  // 是否允许提交
  const isInputValid = computed(() => {
    return inputValue.value.trim() !== ''
  })

  // 切换下拉菜单
  const toggleDropdown = () => {
    dropdownOpen.value = !dropdownOpen.value
  }

  // 切换选择项
  const toggleSelection = (value: string) => {
    selected.value = value // 单选：只保留当前选中的项
  }

  // 提交查询
  const submitQuery = async () => {
    if (!isInputValid.value) return

    loading.value = true
    showResults.value = false

    try {
      // 模拟接口请求
      const mockResponse = await fetchData(inputValue.value, selected.value)

      // 将原始数据映射成 SearchCard 所需格式
      const transformedCandidates = mockResponse.map(item => {
        const paper = item.data
        const author = paper.author_info

        return {
          id: paper.id,
          name: author.author,
          positionTitle: author.position,
          institution: author.affiliation,
          avatarUrl: author.avatar_url,
          skills: paper.tags || [],
          featuredWork: {
            title: paper.title,
            venue: paper.source,
            type: paper.status,
            year: paper.year,
          },
          recommendations: author.recommend_reason || ['No recommendation provided'],
          matchScore: author.score,
          author_ids: author.author_id,
        }
      })

      cards.value = transformedCandidates
      showResults.value = true
    } finally {
      loading.value = false
      inputValue.value = ''
    }
  }

  // 模拟 API 请求函数
  const fetchData = (query: string, filters: string): Promise<Card[]> => {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve([
          {
            data_type: 'paper',
            data: {
              id: 'HVK6nl3i97',
              title:
                'TriForce: Lossless Acceleration of Long Sequence Generation with Hierarchical Speculative Decoding',
              authors: ['Hanshi Sun', 'Zhuoming Chen', 'Yuandong Tian'],
              author_ids: ['~Hanshi_Sun1', '~Zhuoming_Chen1', '~Yuandong_Tian1'],
              keywords: ['Long-context model', 'Speculative decoding', 'LLM efficiency'],
              primary_area: [],
              position: [
                'MS student',
                'PhD student',
                'PhD student',
                'Research Scientist',
                'Researcher',
              ],
              aff: [
                'Carnegie Mellon University',
                'Carnegie Mellon University',
                'Carnegie Mellon University',
                'Meta AI (FAIR)',
                'Meta Facebook',
              ],
              status: 'Poster',
              year: '2024',
              source: 'colm',
              profiles: [
                {
                  author: 'Hanshi Sun',
                  author_id: '~Hanshi_Sun1',
                  scholar_id: 'BjQHEh8AAAAJ',
                  avatar_url:
                    'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=BjQHEh8AAAAJ',
                  position: 'Research Scientist',
                  affiliation: 'ByteDance Inc.',
                  gender: 'Male',
                },
                {
                  author: 'Zhuoming Chen',
                  author_id: '~Zhuoming_Chen1',
                  scholar_id: '4Bb5KRYAAAAJ',
                  avatar_url:
                    'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=4Bb5KRYAAAAJ',
                  position: 'PhD student',
                  affiliation: 'Carnegie Mellon University',
                  gender: 'Male',
                },
                {
                  author: 'Yuandong Tian',
                  author_id: '~Yuandong_Tian1',
                  scholar_id: '0mgEF28AAAAJ',
                  avatar_url:
                    'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=0mgEF28AAAAJ',
                  position: 'Research Scientist',
                  affiliation: 'Meta AI (FAIR)',
                  gender: 'Male',
                },
              ],
              tags: ['LLM', 'ML', 'NLP'],
              author_info: {
                author: 'Yuandong Tian',
                author_id: '~Yuandong_Tian1',
                scholar_id: '0mgEF28AAAAJ',
                avatar_url:
                  'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=0mgEF28AAAAJ',
                position: 'Research Scientist, Meta AI (FAIR)',
                affiliation: 'Meta AI (FAIR)',
                gender: 'Male',
                score: 40,
                recommend_reason: [
                  '🛠️ Brings a can-do attitude and a wide-ranging skill set that elevates team efforts.',
                  '🌟 Always brings a thoughtful perspective and a collaborative spirit to every team or project.',
                  '🎨 Creative thinker who brings fresh ideas and a strong sense of design to their work.',
                  '📦 Dependable with both day-to-day tasks and long-term strategy—knows how to balance both well.',
                ],
              },
            },
          },
          {
            data_type: 'paper',
            data: {
              id: '3go0lhfxd0',
              title:
                'Algorithm Development in Neural Networks: Insights from the Streaming Parity Task',
              authors: ['Andrew M Saxe'],
              author_ids: ['~Andrew_M_Saxe1'],
              keywords: [
                'Out-of-distribution generalization',
                'Algorithm discovery',
                'Deep learning theory',
                'Mechanistic Interpretability',
              ],
              primary_area: ['theory->deep_learning'],
              position: ['PhD student', 'Full Professor'],
              aff: [
                'University College London, University of London',
                'University College London, University of London',
              ],
              status: 'Oral',
              year: '2025',
              source: 'icml',
              profiles: [
                {
                  author: 'Andrew M Saxe',
                  author_id: '~Andrew_M_Saxe1',
                  scholar_id: 'h0Al1fcAAAAJ',
                  avatar_url:
                    'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=h0Al1fcAAAAJ',
                  position: 'Full Professor',
                  affiliation: 'University College London, University of London',
                  gender: 'Male',
                },
              ],
              tags: ['ML', 'NLP', 'CV'],
              author_info: {
                author: 'Andrew M Saxe',
                author_id: '~Andrew_M_Saxe1',
                scholar_id: 'h0Al1fcAAAAJ',
                avatar_url:
                  'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=h0Al1fcAAAAJ',
                position: 'Full Professor, University College London, University of London',
                affiliation: 'University College London, University of London',
                gender: 'Male',
                score: 40,
                recommend_reason: [
                  '🧭 Steady, focused, and principled—leads by example no matter the situation.',
                  '🔗 Builds strong relationships across teams, enabling smoother collaboration and outcomes.',
                  '🔥 Brings energy and positivity to the room, making even tough days feel manageable.',
                ],
              },
            },
          },
          {
            data_type: 'paper',
            data: {
              id: '1VqxIgyQlp',
              title:
                'Kona: An Efficient Privacy-Preservation Framework for KNN Classification by Communication Optimization',
              authors: ['Guopeng Lin', 'Tao Wei'],
              author_ids: ['~Guopeng_Lin1', '~Tao_Wei5'],
              keywords: ['Privacy Preservation', 'K-Nearest Neighbors'],
              primary_area: ['social_aspects->privacy'],
              position: [
                'PhD student',
                'MS student',
                'PhD student',
                'Full Professor',
                'Researcher',
                'Principal Researcher',
                'Principal Researcher',
              ],
              aff: [
                'Fudan University',
                'Fudan University',
                'Fudan University',
                'Fudan University, Tsinghua University',
                'Alibaba Group',
                'Ant Group',
                'Ant Group',
              ],
              status: 'Poster',
              year: '2025',
              source: 'icml',
              profiles: [
                {
                  author: 'Guopeng Lin',
                  author_id: '~Guopeng_Lin1',
                  scholar_id: 'HzjuQ6EAAAAJ',
                  avatar_url:
                    'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=HzjuQ6EAAAAJ',
                  position: 'PhD student',
                  affiliation: 'Fudan University',
                  gender: 'Male',
                },
                {
                  author: 'Tao Wei',
                  author_id: '~Tao_Wei5',
                  scholar_id: 'Ao3wEckAAAAJ',
                  avatar_url:
                    'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=Ao3wEckAAAAJ',
                  position: 'Principal Researcher',
                  affiliation: 'Ant Group',
                  gender: 'Male',
                },
              ],
              tags: ['ML', 'Security', 'Privacy'],
              author_info: {
                author: 'Tao Wei',
                author_id: '~Tao_Wei5',
                scholar_id: 'Ao3wEckAAAAJ',
                avatar_url:
                  'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=Ao3wEckAAAAJ',
                position: 'Principal Researcher, Ant Group',
                affiliation: 'Ant Group',
                gender: 'Male',
                score: 40,
                recommend_reason: [
                  '🌈 Brings a unique perspective that enhances team creativity and decision-making.',
                  '🚀 Demonstrates strong initiative and consistently delivers high-quality results, no matter the challenge.',
                  '🔧 Constantly improving processes, not just doing the work but making it better over time.',
                ],
              },
            },
          },
          {
            data_type: 'paper',
            data: {
              id: '3QkzYBSWqL',
              title: 'Universal Backdoor Attacks',
              authors: ['Nils Lukas', 'Florian Kerschbaum'],
              author_ids: ['~Nils_Lukas1', '~Florian_Kerschbaum1'],
              keywords: ['Backdoor', 'Data poisoning', 'Integrity', 'Image Classification'],
              primary_area: ['societal considerations including fairness, safety, privacy'],
              position: ['MS student', 'PhD student'],
              aff: ['University of Waterloo', 'University of Waterloo'],
              status: 'Poster',
              year: '2024',
              source: 'iclr',
              profiles: [
                {
                  author: 'Nils Lukas',
                  author_id: '~Nils_Lukas1',
                  scholar_id: 'fmlEab4AAAAJ',
                  avatar_url:
                    'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=fmlEab4AAAAJ',
                  position: 'Assistant Professor',
                  affiliation: 'Mohamed bin Zayed University of Artificial Intelligence',
                  gender: 'Male',
                },
                {
                  author: 'Florian Kerschbaum',
                  author_id: '~Florian_Kerschbaum1',
                  scholar_id: 'aP-_mb8AAAAJ',
                  avatar_url:
                    'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=aP-_mb8AAAAJ',
                  position: 'Full Professor',
                  affiliation: 'University of Waterloo',
                  gender: 'Not Specified',
                },
              ],
              tags: ['Security', 'CV', 'ML'],
              author_info: {
                author: 'Nils Lukas',
                author_id: '~Nils_Lukas1',
                scholar_id: 'fmlEab4AAAAJ',
                avatar_url:
                  'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=fmlEab4AAAAJ',
                position:
                  'Assistant Professor, Mohamed bin Zayed University of Artificial Intelligence',
                affiliation: 'Mohamed bin Zayed University of Artificial Intelligence',
                gender: 'Male',
                score: 40,
                recommend_reason: [
                  '🤝 A great communicator who makes collaboration feel easy and productive.',
                  '🏁 Delivers consistently and reliably, even under tight deadlines or shifting priorities.',
                  '🎤 Not afraid to speak up with ideas or concerns that improve outcomes and culture.',
                  '🛰️ Able to zoom in and out with ease, switching from strategy to execution effortlessly.',
                  '🏗️ Helps build strong team culture through trust, transparency, and mutual respect.',
                ],
              },
            },
          },
          {
            data_type: 'paper',
            data: {
              id: '4DoSULcfG6',
              title: 'Chameleon: Increasing Label-Only Membership Leakage with Adaptive Poisoning',
              authors: ['Harsh Chaudhari', 'Giorgio Severi', 'Alina Oprea', 'Jonathan Ullman'],
              author_ids: [
                '~Harsh_Chaudhari1',
                '~Giorgio_Severi1',
                '~Alina_Oprea2',
                '~Jonathan_Ullman1',
              ],
              keywords: ['Privacy Attack', 'Membership Inference', 'Data Poisoning'],
              primary_area: ['societal considerations including fairness, safety, privacy'],
              position: [
                'PhD student',
                'PhD student',
                'Associate Professor',
                'Associate Professor',
              ],
              aff: [
                'Northeastern University',
                'Northeastern University',
                'Northeastern University',
                'Northeastern University',
              ],
              status: 'Poster',
              year: '2024',
              source: 'iclr',
              profiles: [
                {
                  author: 'Harsh Chaudhari',
                  author_id: '~Harsh_Chaudhari1',
                  scholar_id: 'w1lHWJ4AAAAJ',
                  avatar_url:
                    'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=w1lHWJ4AAAAJ',
                  position: 'PhD student',
                  affiliation: 'Northeastern University',
                  gender: 'Male',
                },
                {
                  author: 'Giorgio Severi',
                  author_id: '~Giorgio_Severi1',
                  scholar_id: 'ClHeQx0AAAAJ',
                  avatar_url:
                    'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=ClHeQx0AAAAJ',
                  position: 'Researcher',
                  affiliation: 'Microsoft',
                  gender: 'Male',
                },
                {
                  author: 'Alina Oprea',
                  author_id: '~Alina_Oprea2',
                  scholar_id: '16J3izoAAAAJ',
                  avatar_url:
                    'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=16J3izoAAAAJ',
                  position: 'Full Professor',
                  affiliation: 'Northeastern University',
                  gender: 'Female',
                },
                {
                  author: 'Jonathan Ullman',
                  author_id: '~Jonathan_Ullman1',
                  scholar_id: 'WfS41RAAAAAJ',
                  avatar_url:
                    'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=WfS41RAAAAAJ',
                  position: 'Associate Professor',
                  affiliation: 'Northeastern University',
                  gender: 'Male',
                },
              ],
              tags: ['ML', 'Security', 'Privacy'],
              author_info: {
                author: 'Giorgio Severi',
                author_id: '~Giorgio_Severi1',
                scholar_id: 'ClHeQx0AAAAJ',
                avatar_url:
                  'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=ClHeQx0AAAAJ',
                position: 'Researcher, Microsoft',
                affiliation: 'Microsoft',
                gender: 'Male',
                score: 40,
                recommend_reason: [
                  '🧭 Steady, focused, and principled—leads by example no matter the situation.',
                  '⚡ Quick to adapt and never afraid to take ownership when things get tough.',
                  '🎯 Sets high standards and holds themselves to them, inspiring others to do the same.',
                ],
              },
            },
          },
        ])
      }, 1000)
    })
  }

  // 清空数据的方法
  const resetData = () => {
    inputValue.value = ''
    selected.value = ''
    showResults.value = false
  }

  // 定义卡片类型
  interface Card {
    title: string
    description: string
  }

  // 轮播用户
  interface User {
    id: string
    avatarUrl: string
    name: string
    oldCompanyIcon: string
    newCompanyIcon: string
    salary: string
  }
  // 用户数据
  const users = ref<User[]>([])
  const currentIndex = ref(0)
  const currentUser = computed(() => {
    return users.value[currentIndex.value] || {}
  })
  // 动画方向控制（可选，用于切换方向）
  const transitionName = ref('animate__fadeInUp')

  // 真实数据替换为url，此行可删除
  import avator from '~/assets/image/avator.png'
  // 获取用户列表
  const fetchUserList = async (): Promise<User[]> => {
    // 替换为你自己的 API 请求逻辑
    return new Promise(resolve => {
      setTimeout(() => {
        resolve([
          {
            avatarUrl: avator,
            id: '1',
            name: 'Alice Alen',
            oldCompanyIcon: avator,
            newCompanyIcon: avator,
            salary: '500M',
          },
          {
            id: '2',
            avatarUrl: avator,
            name: 'Grace Markson',
            oldCompanyIcon: avator,
            newCompanyIcon: avator,
            salary: '600M',
          },
          // 可以添加更多数据
        ])
      }, 1000)
    })
  }

  // 初始化：加载用户列表并启动轮播
  onMounted(async () => {
    users.value = await fetchUserList()

    if (users.value.length > 0) {
      setInterval(() => {
        transitionName.value = 'animate__fadeInUp' // 也可以随机切换不同动画
        currentIndex.value = (currentIndex.value + 1) % users.value.length
      }, 20000)
    }
  })

  // 用户点击
  const handleUserClick = (user: User) => {
    console.log('Clicked user:', user)
  }

  defineExpose({ resetData })
</script>

<style scoped>
  .logo-area {
    margin-top: 20%;
  }

  /* 按钮图标控制 */
  .btn-icon-dark {
    display: none;
  }

  .dark .btn-icon-light {
    display: none;
  }

  .dark .btn-icon-dark {
    display: block;
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.3s ease;
  }
  .fade-enter-from,
  .fade-leave-to {
    transform: translateY(100%);
    opacity: 0;
  }
  .fade-enter-to,
  .fade-leave-from {
    transform: translateY(0%);
    opacity: 1;
  }

  @media (max-width: 768px) {
    .logo-area {
      margin-top: 40%;
    }
  }
</style>
